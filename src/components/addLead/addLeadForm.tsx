import { Button } from "@components/common/button";
import { Input } from "@components/common/input";
import { Textarea } from "@components/common/textarea";
import { faChevronDown } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslation } from "react-i18next";

// Mock data for dropdowns
const _FOLLOW_UP_STATUS_OPTIONS = [
  { label: "รอติดตาม", value: "pending" },
  { label: "ติดต่อแล้ว", value: "contacted" },
  { label: "สนใจ", value: "interested" },
  { label: "ไม่สนใจ", value: "not_interested" },
];

const _OPPORTUNITY_OPTIONS = [
  { label: "สูง", value: "high" },
  { label: "ปานกลาง", value: "medium" },
  { label: "ต่ำ", value: "low" },
];

const _CONTACT_CHANNEL_OPTIONS = [
  { label: "Facebook", value: "facebook" },
  { label: "Line", value: "line" },
  { label: "โทรศัพท์", value: "phone" },
  { label: "อีเมล", value: "email" },
  { label: "เว็บไซต์", value: "website" },
];

const _SERVICES_OPTIONS = [
  { label: "ออกแบบเว็บไซต์", value: "web_design" },
  { label: "แอปพลิเคชัน", value: "mobile_app" },
  { label: "การตลาด", value: "marketing" },
  { label: "ที่ปรึกษา", value: "consulting" },
];

export const AddLeadForm = () => {
  const { t } = useTranslation();

  const today = new Date().toISOString().split("T")[0];

  return (
    <form className="flex h-full flex-col gap-6 overflow-hidden p-1">
      <div className="space-y-4">
        {/* ชื่อ - Required field */}
        <div className="flex items-center gap-3">
          <label htmlFor="name" className="flex gap-1">
            <span className="text-h6">{t("addLead.name")}</span>
            <span className="text-error text-h6">*</span>
          </label>
          <Input id="name" type="text" placeholder="Name" className="flex-1" />
        </div>

        {/* สถานะติดตาม */}
        <div className="flex items-center gap-3">
          <label htmlFor="followUpStatus" className="text-h6">
            {t("addLead.followUpStatus")}
          </label>
          <div className="dropdown">
            <Button id="followUpStatus" tabIndex={0} variant="icon">
              <FontAwesomeIcon icon={faChevronDown} />
            </Button>
            <ul className="menu dropdown-content z-1 mt-4 w-fit rounded-box bg-base-200 p-2 shadow-sm">
              {_FOLLOW_UP_STATUS_OPTIONS.map((option) => (
                <li key={option.value}>
                  <p>{option.label}</p>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Row with โอกาส and ช่องทางติดต่อ */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-3">
            <label htmlFor="opportunity" className="text-h6">
              {t("addLead.opportunity")}
            </label>
            <div className="dropdown">
              <Button id="opportunity" tabIndex={0} variant="icon">
                <FontAwesomeIcon icon={faChevronDown} />
              </Button>
              <ul className="menu dropdown-content z-1 mt-4 w-fit rounded-box bg-base-200 p-2 shadow-sm">
                <li>
                  <p>Item 1</p>
                </li>
                <li>
                  <p>Item 2</p>
                </li>
              </ul>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <label htmlFor="contactChannel" className="flex items-center gap-1">
              <span className="text-h6">{t("addLead.contactChannel")}</span>
              <span className="text-error text-h6">*</span>
            </label>
            <div className="dropdown">
              <Button id="contactChannel" tabIndex={0} variant="icon">
                <FontAwesomeIcon icon={faChevronDown} />
              </Button>
              <ul className="menu dropdown-content z-1 mt-4 w-fit rounded-box bg-base-200 p-2 shadow-sm">
                <li>
                  <p>Item 1</p>
                </li>
                <li>
                  <p>Item 2</p>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* บริการที่สนใจ */}
        <div className="flex items-center gap-3">
          <label htmlFor="servicesOfInterest" className="text-h6">
            {t("addLead.servicesOfInterest")}
          </label>
          <div className="dropdown">
            <Button id="servicesOfInterest" tabIndex={0} variant="icon">
              <FontAwesomeIcon icon={faChevronDown} />
            </Button>
            <ul className="menu dropdown-content z-1 mt-4 w-fit rounded-box bg-base-200 p-2 shadow-sm">
              <li>
                <p>Item 1</p>
              </li>
              <li>
                <p>Item 2</p>
              </li>
            </ul>
          </div>
        </div>

        {/* Row with วันที่เริ่ม and วันที่ติดตาม */}
        <div className="flex justify-between gap-4">
          <div className="flex items-center gap-2">
            <label htmlFor="startDate" className="text-h6">
              {t("addLead.startDate")}
            </label>
            <div className="relative">
              <Input
                id="startDate"
                type="date"
                defaultValue={today}
                className="w-32"
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <label htmlFor="followUpDate" className="text-h6">
              {t("addLead.followUpDate")}
            </label>
            <div className="relative">
              <Input id="followUpDate" value={"-"} className="w-32" disabled />
            </div>
          </div>
        </div>

        {/* ติดต่อ */}
        <div className="flex items-center gap-3">
          <label htmlFor="contactInfo" className="text-h6">
            {t("addLead.contactInfo")}
          </label>
          <Input
            id="contactInfo"
            type="text"
            placeholder="Contact"
            className="flex-1"
          />
        </div>
      </div>

      {/* บันทึก */}
      <div className="flex min-h-0 flex-1 flex-col gap-2">
        <label htmlFor="note" className="text-h6">
          {t("addLead.note")}
        </label>
        <Textarea
          id="note"
          placeholder="เพิ่มบันทึกเพิ่มเติม..."
          className="min-h-24 w-full flex-1 resize-none"
        />
      </div>

      {/* Buttons */}
      <div className="flex w-full justify-end gap-3 pt-4">
        <Button variant="outline" className="w-28">
          แบบร่าง
        </Button>
        <Button className="w-28">เพิ่ม</Button>
      </div>
    </form>
  );
};
