import { faChevronDown } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { cn } from "@utils/cn";

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, "size"> {
  className?: string;
  options: SelectOption[];
  placeholder?: string;
  variant?: "primary" | "secondary" | "accent";
  size?: "sm" | "md" | "lg";
}

export const Select = ({
  className,
  options,
  placeholder,
  variant = "primary",
  size = "md",
  ...props
}: SelectProps) => {
  const sizeClasses = {
    lg: "h-10 text-sm",
    md: "h-9 text-body-xs",
    sm: "h-8 text-xs",
  };

  const variantClasses = {
    accent: "select-accent",
    primary: "select-primary",
    secondary: "select-secondary",
  };

  return (
    <div className="relative">
      <select
        {...props}
        className={cn(
          "select w-full appearance-none rounded-lg border-none bg-base-200 pr-8",
          "focus:outline-none focus:ring-2 focus:ring-primary",
          sizeClasses[size],
          variantClasses[variant],
          className,
        )}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      <FontAwesomeIcon
        icon={faChevronDown}
        className="-translate-y-1/2 pointer-events-none absolute top-1/2 right-3 text-neutral text-xs"
      />
    </div>
  );
};
